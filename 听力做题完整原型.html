<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>听力宝 - 听力练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.5;
        }

        /* 头部导航 */
        .header {
            background: #fff;
            padding: 12px 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #007AFF;
            padding: 8px;
        }

        .title {
            font-size: 17px;
            font-weight: 600;
        }

        .progress {
            font-size: 14px;
            color: #666;
        }

        /* 主要内容区域 */
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 16px;
            padding-bottom: 100px;
        }

        /* 播放器区域 */
        .player-section {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .player-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1d1d1f;
        }

        .audio-player {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .play-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #007AFF;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .play-btn:hover {
            transform: scale(1.05);
        }

        .play-btn:active {
            transform: scale(0.95);
        }

        .progress-container {
            flex: 1;
            margin-left: 8px;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: #007AFF;
            width: 0%;
            transition: width 0.1s;
        }

        /* 题目区域 */
        .question-section {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            display: none;
        }

        .question-section.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .question-number {
            font-size: 14px;
            color: #007AFF;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .question-text {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #1d1d1f;
        }

        /* 图片题干 */
        .question-image {
            width: 100%;
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin-bottom: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 选择题样式 */
        .choices {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .choice-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .choice-item:hover {
            background: #f8f9fa;
            border-color: #007AFF;
        }

        .choice-item.selected {
            background: #007AFF10;
            border-color: #007AFF;
        }

        .choice-item.correct {
            background: #34C75910;
            border-color: #34C759;
        }

        .choice-item.incorrect {
            background: #FF3B3010;
            border-color: #FF3B30;
        }

        .choice-item.disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }

        .choice-letter {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #666;
            flex-shrink: 0;
        }

        .choice-item.selected .choice-letter {
            background: #007AFF;
            color: white;
        }

        /* 填空题样式 */
        .fill-blank {
            margin-bottom: 20px;
        }

        .fill-blank-text {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 16px;
        }

        .blank-input {
            border: none;
            border-bottom: 2px solid #e0e0e0;
            padding: 4px 8px;
            font-size: 16px;
            outline: none;
            min-width: 80px;
            text-align: center;
            transition: border-color 0.2s;
            background: transparent;
            margin: 0 4px;
        }

        .blank-input:focus {
            border-bottom-color: #007AFF;
        }

        .blank-input.correct {
            border-bottom-color: #34C759;
            background: #34C75910;
        }

        .blank-input.incorrect {
            border-bottom-color: #FF3B30;
            background: #FF3B3010;
        }

        .blank-input:disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }

        /* 答案解析 */
        .answer-explanation {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
            display: none;
        }

        .answer-explanation.show {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .explanation-title {
            font-size: 14px;
            font-weight: 600;
            color: #007AFF;
            margin-bottom: 8px;
        }

        .explanation-text {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }

        /* 答题统计页面 */
        .result-section {
            display: none;
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .result-section.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .result-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .result-score {
            font-size: 48px;
            font-weight: 700;
            color: #007AFF;
            margin-bottom: 8px;
        }

        .result-desc {
            font-size: 16px;
            color: #666;
        }

        .result-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
        }

        .correct-count {
            color: #34C759;
        }

        .incorrect-count {
            color: #FF3B30;
        }

        /* 题目列表 */
        .question-list {
            margin-bottom: 24px;
        }

        .question-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .question-item:hover {
            background: #f8f9fa;
        }

        .question-item:last-child {
            border-bottom: none;
        }

        .question-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .question-status {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            font-weight: 600;
        }

        .question-status.correct {
            background: #34C759;
        }

        .question-status.incorrect {
            background: #FF3B30;
        }

        .question-status.unanswered {
            background: #999;
        }

        .question-preview {
            font-size: 14px;
            color: #333;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .question-score {
            font-size: 14px;
            font-weight: 600;
        }

        /* 底部操作区 */
        .bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            padding: 16px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 12px;
            z-index: 100;
        }

        .btn {
            flex: 1;
            padding: 14px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007AFF;
            color: white;
        }

        .btn-primary:hover {
            background: #0056CC;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
                padding-bottom: 100px;
            }
            
            .player-section,
            .question-section,
            .result-section {
                padding: 16px;
                margin-bottom: 12px;
            }
            
            .choice-item {
                padding: 12px;
            }
            
            .result-score {
                font-size: 36px;
            }
            
            .result-stats {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        /* 隐藏状态 */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <button class="back-btn" onclick="goBack()">←</button>
        <div class="title" id="pageTitle">听力练习</div>
        <div class="progress" id="progressDisplay">1/5</div>
    </header>

    <!-- 主要内容区域 -->
    <div class="container">
        <!-- 播放器区域 -->
        <div class="player-section" id="playerSection">
            <div class="player-title">请听录音，完成以下题目</div>
            <div class="audio-player">
                <button class="play-btn" id="playBtn" onclick="togglePlay()">
                    <span id="playIcon">▶</span>
                </button>
                <div class="progress-container">
                    <div class="time-display">
                        <span id="currentTime">0:00</span>
                        <span id="totalTime">2:30</span>
                    </div>
                    <div class="progress-bar" onclick="seekTo(event)">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 题目区域 -->
        <!-- 第1题：纯文本选择题 -->
        <div class="question-section active" id="q1">
            <div class="question-number">第1题</div>
            <div class="question-text">What is the man's plan for the weekend?</div>
            <div class="choices">
                <div class="choice-item" onclick="selectChoice(1, this, 'A')">
                    <div class="choice-letter">A</div>
                    <div>Go hiking in the mountains</div>
                </div>
                <div class="choice-item" onclick="selectChoice(1, this, 'B')">
                    <div class="choice-letter">B</div>
                    <div>Visit his parents in the countryside</div>
                </div>
                <div class="choice-item" onclick="selectChoice(1, this, 'C')">
                    <div class="choice-letter">C</div>
                    <div>Stay at home and relax</div>
                </div>
            </div>
            <div class="answer-explanation" id="exp1">
                <div class="explanation-title">答案解析</div>
                <div class="explanation-text">根据对话内容，男士提到"I'm thinking of going hiking with some friends this weekend"，因此正确答案是A。</div>
            </div>
        </div>

        <!-- 第2题：图片选择题 -->
        <div class="question-section" id="q2">
            <div class="question-number">第2题</div>
            <div class="question-text">根据听到的内容，选择正确的地图路线：</div>
            <img class="question-image" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23f0f0f0'/%3E%3Ctext x='150' y='100' text-anchor='middle' font-size='16' fill='%23666'%3E地图图片占位符%3C/text%3E%3C/svg%3E" alt="地图路线">
            <div class="choices">
                <div class="choice-item" onclick="selectChoice(2, this, 'A')">
                    <div class="choice-letter">A</div>
                    <div>Route through the park</div>
                </div>
                <div class="choice-item" onclick="selectChoice(2, this, 'B')">
                    <div class="choice-letter">B</div>
                    <div>Route along the river</div>
                </div>
                <div class="choice-item" onclick="selectChoice(2, this, 'C')">
                    <div class="choice-letter">C</div>
                    <div>Route via the main road</div>
                </div>
            </div>
            <div class="answer-explanation" id="exp2">
                <div class="explanation-title">答案解析</div>
                <div class="explanation-text">录音中提到了"take the scenic route through the central park"，因此应该选择经过公园的路线A。</div>
            </div>
        </div>

        <!-- 第3题：填空题 -->
        <div class="question-section" id="q3">
            <div class="question-number">第3题</div>
            <div class="fill-blank">
                <div class="fill-blank-text">
                    The meeting will be held at 
                    <input type="text" class="blank-input" id="blank3_1" placeholder="时间" oninput="saveFillAnswer(3, 0, this.value)">
                    on 
                    <input type="text" class="blank-input" id="blank3_2" placeholder="日期" oninput="saveFillAnswer(3, 1, this.value)">
                    in Room 301.
                </div>
            </div>
            <div class="answer-explanation" id="exp3">
                <div class="explanation-title">答案解析</div>
                <div class="explanation-text">根据录音内容，会议时间是"3 o'clock"，日期是"next Monday"。</div>
            </div>
        </div>

        <!-- 第4题：图片填空题 -->
        <div class="question-section" id="q4">
            <div class="question-number">第4题</div>
            <div class="question-text">根据图表信息，完成句子：</div>
            <img class="question-image" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23e8f4f8'/%3E%3Crect x='50' y='150' width='20' height='30' fill='%2334C759'/%3E%3Crect x='80' y='120' width='20' height='60' fill='%2334C759'/%3E%3Crect x='110' y='100' width='20' height='80' fill='%2334C759'/%3E%3Crect x='140' y='80' width='20' height='100' fill='%2334C759'/%3E%3Ctext x='150' y='180' text-anchor='middle' font-size='12' fill='%23666'%3E图表占位符%3C/text%3E%3C/svg%3E" alt="数据图表">
            <div class="fill-blank">
                <div class="fill-blank-text">
                    The highest sales occurred in 
                    <input type="text" class="blank-input" id="blank4_1" placeholder="月份" oninput="saveFillAnswer(4, 0, this.value)">
                    with a total of 
                    <input type="text" class="blank-input" id="blank4_2" placeholder="数字" oninput="saveFillAnswer(4, 1, this.value)">
                    units sold.
                </div>
            </div>
            <div class="answer-explanation" id="exp4">
                <div class="explanation-title">答案解析</div>
                <div class="explanation-text">从图表可以看出，12月份的销售额最高，达到了1500个单位。</div>
            </div>
        </div>

        <!-- 第5题：综合选择题 -->
        <div class="question-section" id="q5">
            <div class="question-number">第5题</div>
            <div class="question-text">根据听到的对话和下面的时间表，选择正确的选项：</div>
            <img class="question-image" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 300 150'%3E%3Crect width='300' height='150' fill='%23f9f9f9'/%3E%3Crect x='20' y='30' width='260' height='30' fill='white' stroke='%23ddd' stroke-width='1'/%3E%3Ctext x='30' y='50' font-size='12' fill='%23333'%3E9:00 - 10:30 AM: Team Meeting%3C/text%3E%3Crect x='20' y='65' width='260' height='30' fill='white' stroke='%23ddd' stroke-width='1'/%3E%3Ctext x='30' y='85' font-size='12' fill='%23333'%3E11:00 - 12:00 PM: Client Call%3C/text%3E%3Crect x='20' y='100' width='260' height='30' fill='white' stroke='%23ddd' stroke-width='1'/%3E%3Ctext x='30' y='120' font-size='12' fill='%23333'%3E2:00 - 3:30 PM: Project Review%3C/text%3E%3C/svg%3E" alt="时间表">
            <div class="choices">
                <div class="choice-item" onclick="selectChoice(5, this, 'A')">
                    <div class="choice-letter">A</div>
                    <div>They will meet at 9:00 AM</div>
                </div>
                <div class="choice-item" onclick="selectChoice(5, this, 'B')">
                    <div class="choice-letter">B</div>
                    <div>They will meet at 2:00 PM</div>
                </div>
                <div class="choice-item" onclick="selectChoice(5, this, 'C')">
                    <div class="choice-letter">C</div>
                    <div>They will meet at 11:00 AM</div>
                </div>
            </div>
            <div class="answer-explanation" id="exp5">
                <div class="explanation-title">答案解析</div>
                <div class="explanation-text">对话中提到"Let's meet during the project review at 2 PM"，因此正确答案是B。</div>
            </div>
        </div>

        <!-- 答题统计页面 -->
        <div class="result-section" id="resultSection">
            <div class="result-header">
                <div class="result-score" id="finalScore">0</div>
                <div class="result-desc">答题完成！</div>
            </div>
            
            <div class="result-stats">
                <div class="stat-item">
                    <div class="stat-number correct" id="correctCount">0</div>
                    <div class="stat-label">答对</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number incorrect" id="incorrectCount">0</div>
                    <div class="stat-label">答错</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="accuracyRate">0%</div>
                    <div class="stat-label">正确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalTime">0:00</div>
                    <div class="stat-label">用时</div>
                </div>
            </div>

            <div class="question-list">
                <div class="question-item" onclick="reviewQuestion(1)">
                    <div class="question-info">
                        <div class="question-status" id="status1">1</div>
                        <div class="question-preview">What is the man's plan...</div>
                    </div>
                    <div class="question-score">2分</div>
                </div>
                <div class="question-item" onclick="reviewQuestion(2)">
                    <div class="question-info">
                        <div class="question-status" id="status2">2</div>
                        <div class="question-preview">选择正确的地图路线</div>
                    </div>
                    <div class="question-score">2分</div>
                </div>
                <div class="question-item" onclick="reviewQuestion(3)">
                    <div class="question-info">
                        <div class="question-status" id="status3">3</div>
                        <div class="question-preview">会议时间和日期填空</div>
                    </div>
                    <div class="question-score">2分</div>
                </div>
                <div class="question-item" onclick="reviewQuestion(4)">
                    <div class="question-info">
                        <div class="question-status" id="status4">4</div>
                        <div class="question-preview">图表信息填空题</div>
                    </div>
                    <div class="question-score">2分</div>
                </div>
                <div class="question-item" onclick="reviewQuestion(5)">
                    <div class="question-info">
                        <div class="question-status" id="status5">5</div>
                        <div class="question-preview">时间表选择题</div>
                    </div>
                    <div class="question-score">2分</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部操作区 -->
    <div class="bottom-actions">
        <button class="btn btn-secondary" onclick="showPrevious()" id="prevBtn">上一题</button>
        <button class="btn btn-secondary" onclick="showAnswer()" id="answerBtn">查看答案</button>
        <button class="btn btn-primary" onclick="nextAction()" id="nextBtn">下一题</button>
    </div>

    <script>
        // 全局状态管理
        let currentQuestion = 1;
        const totalQuestions = 5;
        let answers = {};
        let startTime = Date.now();
        let isSubmitted = false;

        // 题目数据
        const questions = [
            { type: 'choice', correct: 'A', score: 2 },
            { type: 'choice', correct: 'A', score: 2 },
            { type: 'fill', correct: ['3 o\'clock', 'next monday'], score: 2 },
            { type: 'fill', correct: ['December', '1500'], score: 2 },
            { type: 'choice', correct: 'B', score: 2 }
        ];

        // 音频播放器功能
        let isPlaying = false;
        let currentTime = 0;
        let totalTime = 150;
        let progressInterval;

        function togglePlay() {
            isPlaying = !isPlaying;
            const playBtn = document.getElementById('playBtn');
            const playIcon = document.getElementById('playIcon');
            
            if (isPlaying) {
                playIcon.textContent = '❚❚';
                startProgress();
            } else {
                playIcon.textContent = '▶';
                stopProgress();
            }
        }

        function startProgress() {
            progressInterval = setInterval(() => {
                if (currentTime < totalTime) {
                    currentTime++;
                    updateProgress();
                } else {
                    stopProgress();
                    isPlaying = false;
                    document.getElementById('playIcon').textContent = '▶';
                }
            }, 1000);
        }

        function stopProgress() {
            clearInterval(progressInterval);
        }

        function updateProgress() {
            const progress = (currentTime / totalTime) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('currentTime').textContent = formatTime(currentTime);
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        function seekTo(event) {
            const rect = event.target.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const percentage = clickX / rect.width;
            currentTime = Math.floor(percentage * totalTime);
            updateProgress();
        }

        // 选择题功能
        function selectChoice(qIndex, element, choice) {
            if (isSubmitted) return;
            
            // 清除其他选择
            const choices = element.parentNode.querySelectorAll('.choice-item');
            choices.forEach(item => {
                item.classList.remove('selected', 'correct', 'incorrect');
                item.classList.add('disabled');
            });
            
            // 添加选择状态
            element.classList.add('selected');
            
            // 保存答案
            answers[qIndex] = {
                type: 'choice',
                answer: choice,
                correct: choice === questions[qIndex - 1].correct
            };

            // 延迟显示正确/错误
            setTimeout(() => {
                choices.forEach(item => item.classList.remove('disabled'));
                
                const correctIndex = questions[qIndex - 1].correct.charCodeAt(0) - 65;
                choices[correctIndex].classList.add('correct');
                
                if (choice !== questions[qIndex - 1].correct) {
                    element.classList.add('incorrect');
                }
                
                updateQuestionStatus();
            }, 1000);
        }

        // 填空题功能
        function saveFillAnswer(qIndex, blankIndex, value) {
            if (!answers[qIndex]) {
                answers[qIndex] = {
                    type: 'fill',
                    answer: [],
                    correct: []
                };
            }
            
            answers[qIndex].answer[blankIndex] = value;
            answers[qIndex].correct[blankIndex] = value.toLowerCase() === questions[qIndex - 1].correct[blankIndex].toLowerCase();
        }

        function checkFillAnswers(qIndex) {
            if (isSubmitted) return;
            
            const question = questions[qIndex - 1];
            const blanks = document.querySelectorAll(`#q${qIndex} .blank-input`);
            
            blanks.forEach((blank, index) => {
                blank.disabled = true;
                const isCorrect = blank.value.toLowerCase() === question.correct[index].toLowerCase();
                
                if (isCorrect) {
                    blank.classList.add('correct');
                } else {
                    blank.classList.add('incorrect');
                    blank.value = question.correct[index];
                }
            });
            
            updateQuestionStatus();
        }

        // 导航功能
        function showPrevious() {
            if (currentQuestion > 1) {
                currentQuestion--;
                updateQuestion();
            }
        }

        function nextAction() {
            if (isSubmitted) {
                // 返回首页或其他操作
                alert('练习已完成！');
                return;
            }
            
            if (currentQuestion < totalQuestions) {
                currentQuestion++;
                updateQuestion();
            } else {
                submitAnswers();
            }
        }

        function updateQuestion() {
            // 隐藏所有题目
            document.querySelectorAll('.question-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 显示当前题目
            const currentSection = document.getElementById(`q${currentQuestion}`);
            if (currentSection) {
                currentSection.classList.add('active');
            }
            
            // 更新进度
            document.getElementById('progressDisplay').textContent = `${currentQuestion}/${totalQuestions}`;
            
            // 更新按钮状态
            document.getElementById('prevBtn').disabled = currentQuestion === 1;
            document.getElementById('nextBtn').textContent = currentQuestion === totalQuestions ? '提交答案' : '下一题';
            
            // 恢复之前的选择状态
            restoreAnswerState();
        }

        function restoreAnswerState() {
            const answer = answers[currentQuestion];
            if (!answer) return;
            
            if (answer.type === 'choice') {
                const choices = document.querySelectorAll(`#q${currentQuestion} .choice-item`);
                choices.forEach((item, index) => {
                    const choice = String.fromCharCode(65 + index);
                    if (choice === answer.answer) {
                        item.classList.add('selected');
                        if (!answer.correct) {
                            item.classList.add('incorrect');
                        }
                    }
                    if (choice === questions[currentQuestion - 1].correct) {
                        item.classList.add('correct');
                    }
                });
            } else if (answer.type === 'fill') {
                const blanks = document.querySelectorAll(`#q${currentQuestion} .blank-input`);
                blanks.forEach((blank, index) => {
                    if (answer.answer[index]) {
                        blank.value = answer.answer[index];
                        blank.classList.add(answer.correct[index] ? 'correct' : 'incorrect');
                        blank.disabled = true;
                    }
                });
            }
        }

        function showAnswer() {
            const explanation = document.getElementById(`exp${currentQuestion}`);
            if (explanation) {
                explanation.classList.add('show');
            }
            
            // 检查填空题答案
            if (questions[currentQuestion - 1].type === 'fill') {
                checkFillAnswers(currentQuestion);
            }
        }

        function submitAnswers() {
            isSubmitted = true;
            
            // 计算得分
            let totalScore = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            
            for (let i = 1; i <= totalQuestions; i++) {
                const answer = answers[i];
                const question = questions[i - 1];
                
                if (!answer) {
                    incorrectCount++;
                    continue;
                }
                
                if (answer.type === 'choice') {
                    if (answer.correct) {
                        totalScore += question.score;
                        correctCount++;
                    } else {
                        incorrectCount++;
                    }
                } else if (answer.type === 'fill') {
                    const allCorrect = answer.correct.every(c => c);
                    if (allCorrect) {
                        totalScore += question.score;
                        correctCount++;
                    } else {
                        incorrectCount++;
                    }
                }
            }
            
            // 更新统计页面
            document.getElementById('finalScore').textContent = totalScore;
            document.getElementById('correctCount').textContent = correctCount;
            document.getElementById('incorrectCount').textContent = incorrectCount;
            document.getElementById('accuracyRate').textContent = 
                Math.round((correctCount / totalQuestions) * 100) + '%';
            document.getElementById('totalTime').textContent = 
                formatTime(Math.floor((Date.now() - startTime) / 1000));
            
            // 更新题目状态
            for (let i = 1; i <= totalQuestions; i++) {
                const statusEl = document.getElementById(`status${i}`);
                const answer = answers[i];
                
                if (!answer) {
                    statusEl.className = 'question-status unanswered';
                    statusEl.textContent = '×';
                } else if (answer.type === 'choice') {
                    statusEl.className = `question-status ${answer.correct ? 'correct' : 'incorrect'}`;
                    statusEl.textContent = answer.correct ? '✓' : '×';
                } else if (answer.type === 'fill') {
                    const allCorrect = answer.correct.every(c => c);
                    statusEl.className = `question-status ${allCorrect ? 'correct' : 'incorrect'}`;
                    statusEl.textContent = allCorrect ? '✓' : '×';
                }
            }
            
            // 显示结果页面
            document.getElementById('playerSection').style.display = 'none';
            document.querySelectorAll('.question-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById('resultSection').classList.add('active');
            
            // 更新按钮
            document.getElementById('prevBtn').style.display = 'none';
            document.getElementById('answerBtn').style.display = 'none';
            document.getElementById('nextBtn').textContent = '完成练习';
            
            // 更新页面标题
            document.getElementById('pageTitle').textContent = '答题结果';
            document.getElementById('progressDisplay').textContent = '';
        }

        function reviewQuestion(qIndex) {
            // 切换到对应题目
            currentQuestion = qIndex;
            
            // 隐藏结果页面，显示题目
            document.getElementById('resultSection').classList.remove('active');
            document.getElementById('playerSection').style.display = 'block';
            
            // 显示题目
            updateQuestion();
            
            // 更新按钮
            document.getElementById('prevBtn').style.display = 'block';
            document.getElementById('answerBtn').style.display = 'block';
            document.getElementById('nextBtn').textContent = currentQuestion === totalQuestions ? '提交答案' : '下一题';
            
            // 更新页面标题
            document.getElementById('pageTitle').textContent = '听力练习';
            document.getElementById('progressDisplay').textContent = `${currentQuestion}/${totalQuestions}`;
            
            // 显示答案和解析
            setTimeout(() => {
                showAnswer();
            }, 100);
        }

        function goBack() {
            if (isSubmitted || confirm('确定要退出练习吗？进度将不会保存。')) {
                window.history.back();
            }
        }

        // 监听填空输入
        document.addEventListener('DOMContentLoaded', function() {
            const blanks = document.querySelectorAll('.blank-input');
            blanks.forEach(blank => {
                blank.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        showAnswer();
                    }
                });
            });
        });

        // 页面离开时清理
        window.addEventListener('beforeunload', function() {
            stopProgress();
        });
    </script>
</body>
</html>