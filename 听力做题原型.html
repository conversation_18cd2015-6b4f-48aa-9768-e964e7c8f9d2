<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>听力宝 - 听力练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.5;
        }

        /* 头部导航 */
        .header {
            background: #fff;
            padding: 12px 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #007AFF;
            padding: 8px;
        }

        .title {
            font-size: 17px;
            font-weight: 600;
        }

        .progress {
            font-size: 14px;
            color: #666;
        }

        /* 主要内容区域 */
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 16px;
        }

        /* 播放器区域 */
        .player-section {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .player-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1d1d1f;
        }

        .audio-player {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .play-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #007AFF;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .play-btn:hover {
            transform: scale(1.05);
        }

        .play-btn:active {
            transform: scale(0.95);
        }

        .progress-container {
            flex: 1;
            margin-left: 8px;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: #007AFF;
            width: 0%;
            transition: width 0.1s;
        }

        /* 题目区域 */
        .question-section {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .question-number {
            font-size: 14px;
            color: #007AFF;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .question-text {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #1d1d1f;
        }

        /* 选择题样式 */
        .choices {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .choice-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .choice-item:hover {
            background: #f8f9fa;
            border-color: #007AFF;
        }

        .choice-item.selected {
            background: #007AFF10;
            border-color: #007AFF;
        }

        .choice-item.correct {
            background: #34C75910;
            border-color: #34C759;
        }

        .choice-item.incorrect {
            background: #FF3B3010;
            border-color: #FF3B30;
        }

        .choice-letter {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #666;
            flex-shrink: 0;
        }

        .choice-item.selected .choice-letter {
            background: #007AFF;
            color: white;
        }

        /* 填空题样式 */
        .fill-blank {
            margin-bottom: 20px;
        }

        .fill-blank-text {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 16px;
        }

        .blank-input {
            border: none;
            border-bottom: 2px solid #e0e0e0;
            padding: 4px 8px;
            font-size: 16px;
            outline: none;
            min-width: 80px;
            text-align: center;
            transition: border-color 0.2s;
            background: transparent;
        }

        .blank-input:focus {
            border-bottom-color: #007AFF;
        }

        .blank-input.correct {
            border-bottom-color: #34C759;
            background: #34C75910;
        }

        .blank-input.incorrect {
            border-bottom-color: #FF3B30;
            background: #FF3B3010;
        }

        /* 答案解析 */
        .answer-explanation {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
            display: none;
        }

        .answer-explanation.show {
            display: block;
        }

        .explanation-title {
            font-size: 14px;
            font-weight: 600;
            color: #007AFF;
            margin-bottom: 8px;
        }

        .explanation-text {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }

        /* 底部操作区 */
        .bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            padding: 16px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 12px;
        }

        .btn {
            flex: 1;
            padding: 14px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007AFF;
            color: white;
        }

        .btn-primary:hover {
            background: #0056CC;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                padding: 12px;
            }
            
            .player-section,
            .question-section {
                padding: 16px;
                margin-bottom: 12px;
            }
            
            .choice-item {
                padding: 12px;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <button class="back-btn" onclick="goBack()">←</button>
        <div class="title">听力练习</div>
        <div class="progress">1/5</div>
    </header>

    <!-- 主要内容区域 -->
    <div class="container">
        <!-- 播放器区域 -->
        <div class="player-section">
            <div class="player-title">请听录音，完成以下题目</div>
            <div class="audio-player">
                <button class="play-btn" id="playBtn" onclick="togglePlay()">
                    <span id="playIcon">▶</span>
                </button>
                <div class="progress-container">
                    <div class="time-display">
                        <span id="currentTime">0:00</span>
                        <span id="totalTime">2:30</span>
                    </div>
                    <div class="progress-bar" onclick="seekTo(event)">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 选择题示例 -->
        <div class="question-section" id="choiceQuestion">
            <div class="question-number">第1题</div>
            <div class="question-text">What is the man's plan for the weekend?</div>
            <div class="choices">
                <div class="choice-item" onclick="selectChoice(this, 'A')">
                    <div class="choice-letter">A</div>
                    <div>Go hiking in the mountains</div>
                </div>
                <div class="choice-item" onclick="selectChoice(this, 'B')">
                    <div class="choice-letter">B</div>
                    <div>Visit his parents in the countryside</div>
                </div>
                <div class="choice-item" onclick="selectChoice(this, 'C')">
                    <div class="choice-letter">C</div>
                    <div>Stay at home and relax</div>
                </div>
            </div>
            <div class="answer-explanation" id="explanation1">
                <div class="explanation-title">答案解析</div>
                <div class="explanation-text">根据对话内容，男士提到"I'm thinking of going hiking with some friends this weekend"，因此正确答案是A。</div>
            </div>
        </div>

        <!-- 填空题示例 -->
        <div class="question-section" id="fillQuestion" style="display: none;">
            <div class="question-number">第2题</div>
            <div class="fill-blank">
                <div class="fill-blank-text">
                    The meeting will be held at 
                    <input type="text" class="blank-input" id="blank1" placeholder="时间">
                    on 
                    <input type="text" class="blank-input" id="blank2" placeholder="日期">
                    in Room 301.
                </div>
            </div>
            <div class="answer-explanation" id="explanation2">
                <div class="explanation-title">答案解析</div>
                <div class="explanation-text">根据录音内容，会议时间是"3 o'clock"，日期是"Friday"。</div>
            </div>
        </div>
    </div>

    <!-- 底部操作区 -->
    <div class="bottom-actions">
        <button class="btn btn-secondary" onclick="showPrevious()">上一题</button>
        <button class="btn btn-secondary" onclick="showAnswer()">查看答案</button>
        <button class="btn btn-primary" onclick="nextQuestion()">下一题</button>
    </div>

    <script>
        // 音频播放器功能
        let isPlaying = false;
        let currentTime = 0;
        let totalTime = 150; // 2:30 in seconds
        let progressInterval;

        function togglePlay() {
            isPlaying = !isPlaying;
            const playBtn = document.getElementById('playBtn');
            const playIcon = document.getElementById('playIcon');
            
            if (isPlaying) {
                playIcon.textContent = '❚❚';
                startProgress();
            } else {
                playIcon.textContent = '▶';
                stopProgress();
            }
        }

        function startProgress() {
            progressInterval = setInterval(() => {
                if (currentTime < totalTime) {
                    currentTime++;
                    updateProgress();
                } else {
                    stopProgress();
                    isPlaying = false;
                    document.getElementById('playIcon').textContent = '▶';
                }
            }, 1000);
        }

        function stopProgress() {
            clearInterval(progressInterval);
        }

        function updateProgress() {
            const progress = (currentTime / totalTime) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('currentTime').textContent = formatTime(currentTime);
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        function seekTo(event) {
            const rect = event.target.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const percentage = clickX / rect.width;
            currentTime = Math.floor(percentage * totalTime);
            updateProgress();
        }

        // 选择题功能
        function selectChoice(element, choice) {
            // 清除其他选择
            const choices = element.parentNode.querySelectorAll('.choice-item');
            choices.forEach(item => item.classList.remove('selected'));
            
            // 添加选择状态
            element.classList.add('selected');
            
            // 这里可以添加答案验证逻辑
            setTimeout(() => {
                if (choice === 'A') {
                    element.classList.add('correct');
                } else {
                    element.classList.add('incorrect');
                    // 标记正确答案
                    choices[0].classList.add('correct');
                }
            }, 500);
        }

        // 填空题功能
        function checkFillBlank() {
            const blank1 = document.getElementById('blank1');
            const blank2 = document.getElementById('blank2');
            
            if (blank1.value.toLowerCase() === '3 o\'clock') {
                blank1.classList.add('correct');
            } else {
                blank1.classList.add('incorrect');
            }
            
            if (blank2.value.toLowerCase() === 'friday') {
                blank2.classList.add('correct');
            } else {
                blank2.classList.add('incorrect');
            }
        }

        // 导航功能
        let currentQuestion = 1;
        const totalQuestions = 5;

        function nextQuestion() {
            if (currentQuestion < totalQuestions) {
                currentQuestion++;
                updateQuestion();
            } else {
                alert('已完成所有题目！');
            }
        }

        function showPrevious() {
            if (currentQuestion > 1) {
                currentQuestion--;
                updateQuestion();
            }
        }

        function updateQuestion() {
            document.querySelector('.progress').textContent = `${currentQuestion}/${totalQuestions}`;
            
            // 切换题目显示
            if (currentQuestion === 1) {
                document.getElementById('choiceQuestion').style.display = 'block';
                document.getElementById('fillQuestion').style.display = 'none';
            } else if (currentQuestion === 2) {
                document.getElementById('choiceQuestion').style.display = 'none';
                document.getElementById('fillQuestion').style.display = 'block';
            }
            
            // 重置状态
            resetQuestionState();
        }

        function resetQuestionState() {
            // 清除选择状态
            document.querySelectorAll('.choice-item').forEach(item => {
                item.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // 清除填空内容
            document.querySelectorAll('.blank-input').forEach(input => {
                input.value = '';
                input.classList.remove('correct', 'incorrect');
            });
            
            // 隐藏答案解析
            document.querySelectorAll('.answer-explanation').forEach(exp => {
                exp.classList.remove('show');
            });
        }

        function showAnswer() {
            const explanation = document.querySelector(`#explanation${currentQuestion}`);
            if (explanation) {
                explanation.classList.add('show');
            }
            
            // 如果是填空题，检查答案
            if (currentQuestion === 2) {
                checkFillBlank();
            }
        }

        function goBack() {
            if (confirm('确定要退出练习吗？')) {
                window.history.back();
            }
        }

        // 监听填空输入
        document.addEventListener('DOMContentLoaded', function() {
            const blanks = document.querySelectorAll('.blank-input');
            blanks.forEach(blank => {
                blank.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        checkFillBlank();
                        showAnswer();
                    }
                });
            });
        });

        // 页面离开时清理
        window.addEventListener('beforeunload', function() {
            stopProgress();
        });
    </script>
</body>
</html>