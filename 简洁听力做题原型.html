<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>听力练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.5;
            overflow: hidden;
        }

        /* 主容器 */
        .container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* 题目卡片 */
        .question-card {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
            margin: 0 20px;
        }

        /* 题量信息 */
        .question-counter {
            text-align: center;
            padding: 16px 20px 0;
            font-size: 14px;
            color: #666;
        }

        /* 录音播放器 */
        .audio-section {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .audio-player {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .play-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: #007AFF;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .play-btn:hover {
            transform: scale(1.05);
        }

        .play-btn:active {
            transform: scale(0.95);
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .progress-bar {
            height: 3px;
            background: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            cursor: pointer;
            margin-top: 4px;
        }

        .progress-fill {
            height: 100%;
            background: #007AFF;
            width: 0%;
            transition: width 0.1s;
        }

        /* 题目内容 */
        .question-content {
            padding: 24px 20px;
        }

        .question-text {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #1d1d1f;
        }

        .question-image {
            width: 100%;
            height: auto;
            border-radius: 8px;
            margin-bottom: 20px;
            background: #f0f0f0;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        /* 选择题 */
        .choices {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .choice-item {
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 15px;
            line-height: 1.4;
        }

        .choice-item:hover {
            background: #f8f9fa;
            border-color: #007AFF;
        }

        .choice-item.selected {
            background: #007AFF10;
            border-color: #007AFF;
        }

        .choice-item.correct {
            background: #34C75910;
            border-color: #34C759;
        }

        .choice-item.incorrect {
            background: #FF3B3010;
            border-color: #FF3B30;
        }

        /* 填空题 */
        .fill-blank {
            margin-bottom: 20px;
        }

        .fill-blank-text {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .blank-input {
            border: none;
            border-bottom: 2px solid #e0e0e0;
            padding: 4px 8px;
            font-size: 16px;
            outline: none;
            min-width: 80px;
            text-align: center;
            transition: border-color 0.2s;
            background: transparent;
            margin: 0 4px;
        }

        .blank-input:focus {
            border-bottom-color: #007AFF;
        }

        .blank-input.correct {
            border-bottom-color: #34C759;
            background: #34C75910;
        }

        .blank-input.incorrect {
            border-bottom-color: #FF3B30;
            background: #FF3B3010;
        }

        /* 左右切换按钮 */
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(0, 0, 0, 0.08);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.25s ease;
            color: #007AFF;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
            backdrop-filter: blur(10px);
        }

        .nav-button:hover {
            background: white;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
            transform: translateY(-50%) scale(1.08);
            border-color: rgba(0, 122, 255, 0.2);
        }

        .nav-button:active {
            transform: translateY(-50%) scale(0.92);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .nav-prev {
            left: 12px;
        }

        .nav-next {
            right: 12px;
        }

        .nav-button:disabled {
            opacity: 0.35;
            cursor: not-allowed;
            background: rgba(255, 255, 255, 0.7);
            color: #c7c7cc;
            transform: translateY(-50%);
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
        }

        .nav-button:disabled:hover {
            transform: translateY(-50%);
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
        }

        /* 答案解析 */
        .answer-explanation {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 3px solid #007AFF;
            display: none;
        }

        .answer-explanation.show {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .explanation-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        /* 隐藏状态 */
        .hidden {
            display: none;
        }

        /* 响应式 */
        @media (max-width: 480px) {
            .question-card {
                margin: 0 28px;
                border-radius: 12px;
            }
            
            .question-content {
                padding: 20px 16px;
            }
            
            .nav-button {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
            
            .nav-prev {
                left: 8px;
            }
            
            .nav-next {
                right: 8px;
            }
        }

        @media (max-width: 375px) {
            .question-card {
                margin: 0 24px;
            }

            .nav-button {
                width: 30px;
                height: 30px;
                font-size: 13px;
            }

            .nav-prev {
                left: 6px;
            }

            .nav-next {
                right: 6px;
            }
        }

        /* 试卷选择界面样式 */
        .paper-selection {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .back-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }



        /* 试卷列表 */
        .paper-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
        }

        .paper-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .paper-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .paper-item:active {
            transform: translateY(0);
        }

        .paper-info {
            flex: 1;
        }

        .paper-title {
            font-size: 15px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .paper-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .paper-tag {
            background: #007AFF;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
        }

        .paper-difficulty {
            color: #FF9500;
            font-size: 12px;
        }

        .paper-time {
            color: #666;
            font-size: 12px;
        }

        .paper-arrow {
            color: #c7c7cc;
            font-size: 18px;
            margin-left: 12px;
        }

        .paper-questions {
            color: #666;
            font-size: 12px;
        }

        /* 添加一些动画效果 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .paper-item {
            animation: slideInUp 0.3s ease-out;
        }

        .paper-item:nth-child(1) { animation-delay: 0.1s; }
        .paper-item:nth-child(2) { animation-delay: 0.2s; }
        .paper-item:nth-child(3) { animation-delay: 0.3s; }
        .paper-item:nth-child(4) { animation-delay: 0.4s; }
        .paper-item:nth-child(5) { animation-delay: 0.5s; }

        .score-circle {
            animation: pulse 2s infinite;
        }

        /* 响应式优化 */
        @media (max-width: 480px) {
            .result-stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .question-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .paper-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 6px;
            }

            .title {
                font-size: 16px;
            }

            /* 移动端下拉选择器优化 */
            select {
                font-size: 12px !important;
                padding: 6px 8px !important;
            }
        }

        /* 答题报告页面样式 */
        .result-page {
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .result-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .share-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .share-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 成绩总览 */
        .result-summary {
            background: white;
            margin: 20px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .score-number {
            font-size: 24px;
            font-weight: 700;
            line-height: 1;
        }

        .score-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .result-stats {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        /* 题目回顾 */
        .question-review {
            background: white;
            margin: 0 20px 20px;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            flex: 1;
            overflow-y: auto;
        }

        .review-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
        }

        .question-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 12px;
        }

        .question-bubble {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            font-weight: 600;
        }

        .question-bubble.correct {
            background: #34C759;
            color: white;
        }

        .question-bubble.incorrect {
            background: #FF3B30;
            color: white;
        }

        .question-bubble:hover {
            transform: scale(1.1);
        }

        .question-num {
            font-size: 14px;
        }

        .question-status {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
        }

        .question-bubble.correct .question-status {
            color: #34C759;
        }

        .question-bubble.incorrect .question-status {
            color: #FF3B30;
        }

        /* 操作按钮 */
        .result-actions {
            padding: 20px;
            display: flex;
            gap: 12px;
        }

        .btn {
            flex: 1;
            padding: 14px;
            border-radius: 12px;
            border: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-primary {
            background: white;
            color: #667eea;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <!-- 试卷选择界面 -->
    <div class="paper-selection" id="paperSelection">
        <div class="header">
            <button class="back-btn" onclick="goBack()">‹</button>
            <h1 class="title">听力宝 - 选择试卷</h1>
        </div>

        <!-- 简化的搜索和筛选栏 -->
        <div style="padding: 16px 20px; background: rgba(255,255,255,0.1);">
            <!-- 搜索框 -->
            <div style="position: relative; margin-bottom: 12px;">
                <input type="text" id="searchInput" placeholder="搜索试卷..."
                       style="width: 100%; padding: 10px 16px 10px 36px; border: none; border-radius: 8px;
                              background: rgba(255,255,255,0.9); font-size: 14px; outline: none;"
                       oninput="searchPapers(this.value)">
                <div style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%);
                           color: #666; font-size: 14px;">🔍</div>
            </div>

            <!-- 下拉筛选器 -->
            <div style="display: flex; gap: 6px; flex-wrap: wrap;">
                <select id="gradeFilter" onchange="applyFilters()"
                        style="flex: 1; min-width: 70px; padding: 8px 8px; border: none; border-radius: 6px;
                               background: rgba(255,255,255,0.9); font-size: 12px; outline: none;">
                    <option value="all">年级</option>
                    <option value="grade7">初一</option>
                    <option value="grade8">初二</option>
                    <option value="grade9">初三</option>
                    <option value="grade10">高一</option>
                    <option value="grade11">高二</option>
                    <option value="grade12">高三</option>
                </select>

                <select id="subjectFilter" onchange="applyFilters()"
                        style="flex: 1; min-width: 70px; padding: 8px 8px; border: none; border-radius: 6px;
                               background: rgba(255,255,255,0.9); font-size: 12px; outline: none;">
                    <option value="all">科目</option>
                    <option value="english">英语</option>
                    <option value="chinese">语文</option>
                    <option value="math">数学</option>
                </select>

                <select id="provinceFilter" onchange="applyFilters()"
                        style="flex: 1; min-width: 60px; padding: 8px 8px; border: none; border-radius: 6px;
                               background: rgba(255,255,255,0.9); font-size: 12px; outline: none;">
                    <option value="all">省</option>
                    <option value="beijing">北京</option>
                    <option value="shanghai">上海</option>
                    <option value="guangdong">广东</option>
                    <option value="jiangsu">江苏</option>
                    <option value="zhejiang">浙江</option>
                </select>

                <select id="cityFilter" onchange="applyFilters()"
                        style="flex: 1; min-width: 70px; padding: 8px 8px; border: none; border-radius: 6px;
                               background: rgba(255,255,255,0.9); font-size: 12px; outline: none;">
                    <option value="all">市区</option>
                    <option value="haidian">海淀区</option>
                    <option value="chaoyang">朝阳区</option>
                    <option value="xicheng">西城区</option>
                    <option value="dongcheng">东城区</option>
                    <option value="huangpu">黄浦区</option>
                    <option value="pudong">浦东新区</option>
                </select>
            </div>
        </div>

        <!-- 试卷列表 -->
        <div class="paper-list" id="paperList">
            <div class="paper-item" onclick="selectPaper(1)" data-grade="grade12" data-subject="english" data-province="beijing" data-city="haidian">
                <div class="paper-info">
                    <div class="paper-title">2024年北京市高考英语听力模拟卷一</div>
                    <div class="paper-meta">
                        <span class="paper-tag">北京</span>
                        <span class="paper-difficulty">★★★☆☆</span>
                        <span class="paper-time">20分钟</span>
                        <span class="paper-questions">15题</span>
                    </div>
                </div>
                <div class="paper-arrow">›</div>
            </div>

            <div class="paper-item" onclick="selectPaper(2)" data-grade="grade12" data-subject="english" data-province="shanghai" data-city="huangpu">
                <div class="paper-info">
                    <div class="paper-title">2024年上海市春考英语听力真题</div>
                    <div class="paper-meta">
                        <span class="paper-tag">上海</span>
                        <span class="paper-difficulty">★★★★☆</span>
                        <span class="paper-time">25分钟</span>
                        <span class="paper-questions">20题</span>
                    </div>
                </div>
                <div class="paper-arrow">›</div>
            </div>

            <div class="paper-item" onclick="selectPaper(3)" data-grade="grade11" data-subject="english" data-province="guangdong" data-city="all">
                <div class="paper-info">
                    <div class="paper-title">广东省英语听说考试模拟题</div>
                    <div class="paper-meta">
                        <span class="paper-tag">广东</span>
                        <span class="paper-difficulty">★★★☆☆</span>
                        <span class="paper-time">18分钟</span>
                        <span class="paper-questions">12题</span>
                    </div>
                </div>
                <div class="paper-arrow">›</div>
            </div>

            <div class="paper-item" onclick="selectPaper(4)" data-grade="grade10" data-subject="english" data-province="jiangsu" data-city="all">
                <div class="paper-info">
                    <div class="paper-title">江苏省英语听力基础训练</div>
                    <div class="paper-meta">
                        <span class="paper-tag">江苏</span>
                        <span class="paper-difficulty">★★☆☆☆</span>
                        <span class="paper-time">15分钟</span>
                        <span class="paper-questions">10题</span>
                    </div>
                </div>
                <div class="paper-arrow">›</div>
            </div>

            <div class="paper-item" onclick="selectPaper(5)" data-grade="grade12" data-subject="english" data-province="zhejiang" data-city="all">
                <div class="paper-info">
                    <div class="paper-title">2024年浙江省高考英语听力真题</div>
                    <div class="paper-meta">
                        <span class="paper-tag">浙江</span>
                        <span class="paper-difficulty">★★★★★</span>
                        <span class="paper-time">30分钟</span>
                        <span class="paper-questions">20题</span>
                    </div>
                </div>
                <div class="paper-arrow">›</div>
            </div>

            <div class="paper-item" onclick="selectPaper(6)" data-grade="grade9" data-subject="english" data-province="beijing" data-city="chaoyang">
                <div class="paper-info">
                    <div class="paper-title">北京朝阳区中考英语听力专项训练</div>
                    <div class="paper-meta">
                        <span class="paper-tag">北京</span>
                        <span class="paper-difficulty">★★☆☆☆</span>
                        <span class="paper-time">15分钟</span>
                        <span class="paper-questions">12题</span>
                    </div>
                </div>
                <div class="paper-arrow">›</div>
            </div>
        </div>
    </div>

    <!-- 答题结果界面 -->
    <div class="result-page hidden" id="resultPage">
        <div class="result-header">
            <button class="back-btn" onclick="backToPaperSelection()">‹</button>
            <h1 class="title">听力宝 - 答题报告</h1>
            <div class="share-btn" onclick="shareResult()">分享</div>
        </div>

        <div class="result-summary">
            <div class="score-circle">
                <div class="score-number" id="finalScore">85</div>
                <div class="score-label">分</div>
            </div>
            <div class="result-stats">
                <div class="stat-item">
                    <div class="stat-number" id="correctCount">4</div>
                    <div class="stat-label">答对</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="incorrectCount">1</div>
                    <div class="stat-label">答错</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="accuracyRate">80%</div>
                    <div class="stat-label">正确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalTime">8:32</div>
                    <div class="stat-label">用时</div>
                </div>
            </div>
        </div>

        <!-- 学习建议 -->
        <div class="learning-advice" style="background: white; margin: 0 20px 16px; border-radius: 12px; padding: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
                <span style="font-size: 16px;">💡</span>
                <span style="font-weight: 600; color: #1d1d1f;">学习建议</span>
            </div>
            <div id="adviceText" style="font-size: 14px; color: #666; line-height: 1.5;">
                根据您的答题情况，建议重点练习对话理解和细节捕捉能力。可以多听一些日常对话材料，提高对关键信息的敏感度。
            </div>
        </div>

        <div class="question-review">
            <div class="review-title">题目回顾 - 点击查看详情</div>
            <div class="question-grid" id="questionGrid">
                <div class="question-bubble" onclick="reviewQuestion(1)" id="bubble-1">
                    <span class="question-num">1</span>
                    <span class="question-status">?</span>
                </div>
                <div class="question-bubble" onclick="reviewQuestion(2)" id="bubble-2">
                    <span class="question-num">2</span>
                    <span class="question-status">?</span>
                </div>
                <div class="question-bubble" onclick="reviewQuestion(3)" id="bubble-3">
                    <span class="question-num">3</span>
                    <span class="question-status">?</span>
                </div>
                <div class="question-bubble" onclick="reviewQuestion(4)" id="bubble-4">
                    <span class="question-num">4</span>
                    <span class="question-status">?</span>
                </div>
                <div class="question-bubble" onclick="reviewQuestion(5)" id="bubble-5">
                    <span class="question-num">5</span>
                    <span class="question-status">?</span>
                </div>
            </div>

            <!-- 错题收藏提示 -->
            <div id="wrongQuestionTip" style="margin-top: 16px; padding: 12px; background: #FFF3CD; border-radius: 8px; border-left: 3px solid #FF9500; display: none;">
                <div style="font-size: 14px; color: #856404;">
                    <strong>📚 错题收藏</strong><br>
                    发现 <span id="wrongCount">1</span> 道错题已自动收藏到错题本，建议稍后重点复习。
                </div>
            </div>
        </div>

        <div class="result-actions">
            <button class="btn btn-secondary" onclick="retryPaper()">重新答题</button>
            <button class="btn btn-primary" onclick="backToPaperSelection()">选择其他试卷</button>
        </div>

        <!-- 学习统计按钮 -->
        <div style="text-align: center; padding: 0 20px 20px;">
            <button onclick="showLearningStats()"
                    style="background: none; border: 1px solid rgba(255,255,255,0.3);
                           color: white; padding: 8px 16px; border-radius: 20px;
                           font-size: 14px; cursor: pointer; transition: all 0.2s;"
                    onmouseover="this.style.background='rgba(255,255,255,0.1)'"
                    onmouseout="this.style.background='none'">
                📊 查看学习统计
            </button>
        </div>

        <!-- 返回做题按钮 -->
        <div style="position: fixed; bottom: 20px; right: 20px; z-index: 100;">
            <button onclick="backToQuestion()"
                    style="width: 48px; height: 48px; border-radius: 50%; background: #007AFF;
                           border: none; color: white; font-size: 18px; cursor: pointer;
                           box-shadow: 0 4px 12px rgba(0,122,255,0.3); transition: all 0.2s;"
                    onmouseover="this.style.transform='scale(1.1)'"
                    onmouseout="this.style.transform='scale(1)'">
                📝
            </button>
        </div>
    </div>

    <!-- 原有的做题界面 -->
    <div class="container hidden" id="questionContainer">
        <!-- 进度指示器 -->
        <div style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
                    background: rgba(255,255,255,0.95); padding: 8px 16px; border-radius: 20px;
                    box-shadow: 0 2px 12px rgba(0,0,0,0.1); z-index: 100; backdrop-filter: blur(10px);">
            <div style="display: flex; align-items: center; gap: 8px; font-size: 14px; color: #333;">
                <span>📝</span>
                <span id="progressText">1/5</span>
                <div style="width: 60px; height: 4px; background: #e0e0e0; border-radius: 2px; overflow: hidden;">
                    <div id="progressBar" style="height: 100%; background: #007AFF; width: 20%; transition: width 0.3s;"></div>
                </div>
                <span id="timeElapsed" style="color: #666; font-size: 12px;">00:00</span>
            </div>
        </div>

        <!-- 上一题按钮 -->
        <button class="nav-button nav-prev" onclick="previousQuestion()" id="prevBtn">‹</button>

        <!-- 题目卡片 -->
        <div class="question-card">
            <!-- 第1题：文本选择题 -->
            <div class="question-content" id="q1">
                <div class="question-counter">1/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(1)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time1">0:00</span>
                                <span>2:30</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(1, event)">
                                <div class="progress-fill" id="progress1"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">What is the man's plan for the weekend?</div>
                    <div class="choices">
                        <div class="choice-item" onclick="selectChoice(1, this, 'A')">Go hiking in the mountains</div>
                        <div class="choice-item" onclick="selectChoice(1, this, 'B')">Visit his parents in the countryside</div>
                        <div class="choice-item" onclick="selectChoice(1, this, 'C')">Stay at home and relax</div>
                    </div>
                    <div class="answer-explanation" id="exp1">
                        <div class="explanation-text">根据对话内容，男士提到"I'm thinking of going hiking with some friends this weekend"，因此正确答案是A。</div>
                    </div>
                </div>
            </div>

            <!-- 第2题：图片选择题 -->
            <div class="question-content hidden" id="q2">
                <div class="question-counter">2/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(2)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time2">0:00</span>
                                <span>1:45</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(2, event)">
                                <div class="progress-fill" id="progress2"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">根据听到的内容，选择正确的地图路线：</div>
                    <div class="question-image">地图图片占位符</div>
                    <div class="choices">
                        <div class="choice-item" onclick="selectChoice(2, this, 'A')">Route through the park</div>
                        <div class="choice-item" onclick="selectChoice(2, this, 'B')">Route along the river</div>
                        <div class="choice-item" onclick="selectChoice(2, this, 'C')">Route via the main road</div>
                    </div>
                    <div class="answer-explanation" id="exp2">
                        <div class="explanation-text">录音中提到了"take the scenic route through the central park"，因此应该选择经过公园的路线A。</div>
                    </div>
                </div>
            </div>

            <!-- 第3题：填空题 -->
            <div class="question-content hidden" id="q3">
                <div class="question-counter">3/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(3)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time3">0:00</span>
                                <span>1:20</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(3, event)">
                                <div class="progress-fill" id="progress3"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">完成句子：</div>
                    <div class="fill-blank">
                        <div class="fill-blank-text">
                            The meeting will be held at 
                            <input type="text" class="blank-input" id="blank3_1" placeholder="时间" oninput="saveFillAnswer(3, 0, this.value)">
                            on 
                            <input type="text" class="blank-input" id="blank3_2" placeholder="日期" oninput="saveFillAnswer(3, 1, this.value)">
                            in Room 301.
                        </div>
                    </div>
                    <div class="answer-explanation" id="exp3">
                        <div class="explanation-text">根据录音内容，会议时间是"3 o'clock"，日期是"next Monday"。</div>
                    </div>
                </div>
            </div>

            <!-- 第4题：图片填空题 -->
            <div class="question-content hidden" id="q4">
                <div class="question-counter">4/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(4)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time4">0:00</span>
                                <span>2:10</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(4, event)">
                                <div class="progress-fill" id="progress4"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">根据图表信息，完成句子：</div>
                    <div class="question-image">数据图表占位符</div>
                    <div class="fill-blank">
                        <div class="fill-blank-text">
                            The highest sales occurred in 
                            <input type="text" class="blank-input" id="blank4_1" placeholder="月份" oninput="saveFillAnswer(4, 0, this.value)">
                            with a total of 
                            <input type="text" class="blank-input" id="blank4_2" placeholder="数字" oninput="saveFillAnswer(4, 1, this.value)">
                            units sold.
                        </div>
                    </div>
                    <div class="answer-explanation" id="exp4">
                        <div class="explanation-text">从图表可以看出，12月份的销售额最高，达到了1500个单位。</div>
                    </div>
                </div>
            </div>

            <!-- 第5题：综合选择题 -->
            <div class="question-content hidden" id="q5">
                <div class="question-counter">5/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(5)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time5">0:00</span>
                                <span>1:55</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(5, event)">
                                <div class="progress-fill" id="progress5"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">根据听到的对话和下面的时间表，选择正确的选项：</div>
                    <div class="question-image">时间表占位符</div>
                    <div class="choices">
                        <div class="choice-item" onclick="selectChoice(5, this, 'A')">They will meet at 9:00 AM</div>
                        <div class="choice-item" onclick="selectChoice(5, this, 'B')">They will meet at 2:00 PM</div>
                        <div class="choice-item" onclick="selectChoice(5, this, 'C')">They will meet at 11:00 AM</div>
                    </div>
                    <div class="answer-explanation" id="exp5">
                        <div class="explanation-text">对话中提到"Let's meet during the project review at 2 PM"，因此正确答案是B。</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下一题按钮 -->
        <button class="nav-button nav-next" onclick="nextQuestion()" id="nextBtn">›</button>

        <!-- 帮助按钮 -->
        <div style="position: fixed; bottom: 20px; left: 20px; z-index: 100;">
            <button onclick="toggleHelp()"
                    style="width: 48px; height: 48px; border-radius: 50%; background: rgba(0,122,255,0.9);
                           border: none; color: white; font-size: 18px; cursor: pointer;
                           box-shadow: 0 4px 12px rgba(0,122,255,0.3); transition: all 0.2s;"
                    onmouseover="this.style.transform='scale(1.1)'"
                    onmouseout="this.style.transform='scale(1)'">
                ❓
            </button>
        </div>



        <!-- 帮助面板 -->
        <div id="helpPanel" style="position: fixed; bottom: 80px; left: 20px; z-index: 101;
                                   background: white; border-radius: 12px; padding: 16px;
                                   box-shadow: 0 4px 20px rgba(0,0,0,0.15); max-width: 280px;
                                   display: none; animation: slideInUp 0.3s ease-out;">
            <div style="font-weight: 600; margin-bottom: 12px; color: #1d1d1f;">快捷键提示</div>
            <div style="font-size: 14px; color: #666; line-height: 1.5;">
                <div>← → 切换题目</div>
                <div>空格键 播放/暂停音频</div>
                <div>1/2/3 快速选择选项</div>
                <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #e0e0e0; font-size: 12px;">
                    💡 答题会自动保存进度
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let currentQuestion = 1;
        const totalQuestions = 5;
        let answers = {};
        let audioStates = {};

        // 页面状态管理
        let currentPage = 'paperSelection'; // paperSelection, question, result
        let selectedPaper = null;
        let startTime = null;

        // 搜索功能
        function searchPapers(query) {
            const papers = document.querySelectorAll('.paper-item');
            const searchTerm = query.toLowerCase();

            papers.forEach(paper => {
                const title = paper.querySelector('.paper-title').textContent.toLowerCase();
                const tag = paper.querySelector('.paper-tag').textContent.toLowerCase();

                if (title.includes(searchTerm) || tag.includes(searchTerm)) {
                    paper.style.display = 'flex';
                } else {
                    paper.style.display = 'none';
                }
            });

            // 搜索后也要应用筛选
            if (query.trim() === '') {
                applyFilters();
            }
        }

        // 更新的筛选功能
        function applyFilters() {
            const gradeFilter = document.getElementById('gradeFilter').value;
            const subjectFilter = document.getElementById('subjectFilter').value;
            const provinceFilter = document.getElementById('provinceFilter').value;
            const cityFilter = document.getElementById('cityFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();

            const papers = document.querySelectorAll('.paper-item');

            papers.forEach(paper => {
                const paperGrade = paper.dataset.grade;
                const paperSubject = paper.dataset.subject;
                const paperProvince = paper.dataset.province;
                const paperCity = paper.dataset.city;
                const title = paper.querySelector('.paper-title').textContent.toLowerCase();
                const tag = paper.querySelector('.paper-tag').textContent.toLowerCase();

                const gradeMatch = gradeFilter === 'all' || paperGrade === gradeFilter;
                const subjectMatch = subjectFilter === 'all' || paperSubject === subjectFilter;
                const provinceMatch = provinceFilter === 'all' || paperProvince === provinceFilter;
                const cityMatch = cityFilter === 'all' || paperCity === cityFilter || paperCity === 'all';
                const searchMatch = searchTerm === '' || title.includes(searchTerm) || tag.includes(searchTerm);

                if (gradeMatch && subjectMatch && provinceMatch && cityMatch && searchMatch) {
                    paper.style.display = 'flex';
                } else {
                    paper.style.display = 'none';
                }
            });
        }

        function selectPaper(paperId) {
            console.log('选择试卷:', paperId);
            selectedPaper = paperId;
            startTime = Date.now();

            // 隐藏试卷选择界面，显示做题界面
            const paperSelection = document.getElementById('paperSelection');
            const questionContainer = document.getElementById('questionContainer');
            
            if (paperSelection && questionContainer) {
                paperSelection.style.display = 'none';
                paperSelection.classList.add('hidden');
                
                questionContainer.style.display = 'block';
                questionContainer.classList.remove('hidden');
            }

            currentPage = 'question';
            currentQuestion = 1;
            answers = {};
            audioStates = {};

            // 使用 requestAnimationFrame 确保DOM更新完成
            requestAnimationFrame(() => {
                // 重置题目状态
                resetAllQuestions();

                // 更新进度指示器
                updateProgressIndicator();
                
                console.log('已进入答题界面');
            });
        }

        function resetAllQuestions() {
            // 隐藏所有题目
            for (let i = 1; i <= totalQuestions; i++) {
                document.getElementById(`q${i}`).classList.add('hidden');
                
                // 清除选择状态
                const choices = document.querySelectorAll(`#q${i} .choice-item`);
                choices.forEach(item => {
                    item.classList.remove('selected', 'correct', 'incorrect');
                    item.style.pointerEvents = 'auto';
                });
                
                // 清除填空内容
                const blanks = document.querySelectorAll(`#q${i} .blank-input`);
                blanks.forEach(blank => {
                    blank.value = '';
                    blank.classList.remove('correct', 'incorrect');
                    blank.disabled = false;
                });
                
                // 隐藏解析
                document.getElementById(`exp${i}`).classList.remove('show');
            }
            
            // 显示第一题
            document.getElementById('q1').classList.remove('hidden');

            // 安全地更新按钮状态
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            if (prevBtn) prevBtn.disabled = true;
            if (nextBtn) nextBtn.disabled = false;
        }

        // 题目答案
        const correctAnswers = {
            1: 'A',
            2: 'A',
            3: ['3 o\'clock', 'next monday'],
            4: ['December', '1500'],
            5: 'B'
        };

        // 音频播放功能
        function togglePlay(qIndex) {
            if (!audioStates[qIndex]) {
                audioStates[qIndex] = {
                    isPlaying: false,
                    currentTime: 0,
                    totalTime: 150, // 模拟总时长
                    interval: null
                };
            }

            const state = audioStates[qIndex];
            state.isPlaying = !state.isPlaying;

            const playBtn = document.querySelector(`#q${qIndex} .play-btn`);
            const timeEl = document.getElementById(`time${qIndex}`);
            const progressEl = document.getElementById(`progress${qIndex}`);

            if (state.isPlaying) {
                playBtn.innerHTML = '❚❚';
                state.interval = setInterval(() => {
                    if (state.currentTime < state.totalTime) {
                        state.currentTime++;
                        updateAudioDisplay(qIndex);
                    } else {
                        stopAudio(qIndex);
                    }
                }, 100);
            } else {
                stopAudio(qIndex);
            }
        }

        function stopAudio(qIndex) {
            const state = audioStates[qIndex];
            if (state) {
                state.isPlaying = false;
                clearInterval(state.interval);
                const playBtn = document.querySelector(`#q${qIndex} .play-btn`);
                playBtn.innerHTML = '▶';
            }
        }

        function updateAudioDisplay(qIndex) {
            const state = audioStates[qIndex];
            const timeEl = document.getElementById(`time${qIndex}`);
            const progressEl = document.getElementById(`progress${qIndex}`);
            
            const mins = Math.floor(state.currentTime / 60);
            const secs = state.currentTime % 60;
            timeEl.textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
            
            const progress = (state.currentTime / state.totalTime) * 100;
            progressEl.style.width = progress + '%';
        }

        function seekTo(qIndex, event) {
            if (!audioStates[qIndex]) return;
            
            const rect = event.target.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const percentage = clickX / rect.width;
            
            audioStates[qIndex].currentTime = Math.floor(percentage * audioStates[qIndex].totalTime);
            updateAudioDisplay(qIndex);
        }

        // 选择题功能
        function selectChoice(qIndex, element, choice) {
            // 清除其他选择
            const choices = element.parentNode.querySelectorAll('.choice-item');
            choices.forEach(item => {
                item.classList.remove('selected', 'correct', 'incorrect');
                item.style.pointerEvents = 'none';
            });

            // 添加选择状态
            element.classList.add('selected');

            // 保存答案
            answers[qIndex] = {
                type: 'choice',
                answer: choice,
                correct: choice === correctAnswers[qIndex]
            };

            // 自动保存进度
            saveProgress();

            // 显示正确答案
            setTimeout(() => {
                choices.forEach(item => item.style.pointerEvents = 'auto');
                
                const correctChoice = Array.from(choices).find(c => 
                    c.textContent.startsWith(correctAnswers[qIndex] + '.') ||
                    ['A', 'B', 'C'].indexOf(correctAnswers[qIndex]) === Array.from(choices).indexOf(c)
                );
                if (correctChoice) correctChoice.classList.add('correct');
                
                if (choice !== correctAnswers[qIndex]) {
                    element.classList.add('incorrect');
                }

                // 显示解析
                document.getElementById(`exp${qIndex}`).classList.add('show');
                
                // 自动进入下一题或完成
                setTimeout(() => {
                    if (qIndex < totalQuestions) {
                        nextQuestion();
                    } else {
                        completeQuiz();
                    }
                }, 2000); // 2秒后自动完成
            }, 800);
        }

        // 填空题功能
        function saveFillAnswer(qIndex, blankIndex, value) {
            if (!answers[qIndex]) {
                answers[qIndex] = {
                    type: 'fill',
                    answer: ['', ''],
                    correct: [false, false]
                };
            }
            
            answers[qIndex].answer[blankIndex] = value;
            answers[qIndex].correct[blankIndex] =
                value.toLowerCase() === correctAnswers[qIndex][blankIndex].toLowerCase();

            // 自动保存进度
            saveProgress();
        }

        function checkFillAnswers(qIndex) {
            const blanks = document.querySelectorAll(`#q${qIndex} .blank-input`);
            const allFilled = Array.from(blanks).every(b => b.value.trim());
            
            if (!allFilled) return;

            blanks.forEach((blank, index) => {
                blank.disabled = true;
                const isCorrect = blank.value.toLowerCase() === correctAnswers[qIndex][index].toLowerCase();
                
                if (isCorrect) {
                    blank.classList.add('correct');
                } else {
                    blank.classList.add('incorrect');
                    blank.value = correctAnswers[qIndex][index];
                }
            });

            document.getElementById(`exp${qIndex}`).classList.add('show');
            
            // 自动进入下一题或完成
            setTimeout(() => {
                console.log(`🔍 填空题检查完成状态: 当前题目${qIndex}, 总题目${totalQuestions}`);
                if (qIndex < totalQuestions) {
                    console.log('📝 进入下一题');
                    nextQuestion();
                } else {
                    console.log('🎯 最后一题完成，开始跳转到结果页');
                    completeQuiz();
                }
            }, 800); // 完成后自动跳转
        }

        // 题目切换
        function previousQuestion() {
            if (currentQuestion > 1) {
                switchQuestion(currentQuestion - 1);
            }
        }

        function nextQuestion() {
            console.log(`尝试下一题: 当前第${currentQuestion}题，总${totalQuestions}题`);
            if (currentQuestion < totalQuestions) {
                console.log(`切换到第${currentQuestion + 1}题`);
                switchQuestion(currentQuestion + 1);
            } else {
                console.log('🎯 已到达最后一题，通过自动完成触发');
                // 最后一题由自动完成逻辑处理，这里不做操作
            }
        }

        function switchQuestion(newIndex) {
            // 停止当前音频
            stopAudio(currentQuestion);

            // 隐藏当前题目
            document.getElementById(`q${currentQuestion}`).classList.add('hidden');

            // 显示新题目
            document.getElementById(`q${newIndex}`).classList.remove('hidden');

            currentQuestion = newIndex;

            // 更新按钮状态
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            if (prevBtn) prevBtn.disabled = currentQuestion === 1;
            if (nextBtn) nextBtn.disabled = currentQuestion === totalQuestions;

            // 更新进度指示器
            updateProgressIndicator();

            // 恢复之前的选择状态
            restoreAnswerState();
        }

        // 更新进度指示器
        function updateProgressIndicator() {
            const progressText = document.getElementById('progressText');
            const progressBar = document.getElementById('progressBar');

            if (progressText && progressBar) {
                progressText.textContent = `${currentQuestion}/${totalQuestions}`;
                const progress = (currentQuestion / totalQuestions) * 100;
                progressBar.style.width = progress + '%';
            }
        }

        // 更新计时器
        function updateTimer() {
            if (startTime && currentPage === 'question') {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                const mins = Math.floor(elapsed / 60);
                const secs = elapsed % 60;
                document.getElementById('timeElapsed').textContent =
                    `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }
        }

        // 启动计时器
        function startTimer() {
            setInterval(updateTimer, 1000);
        }

        // 帮助面板功能
        function toggleHelp() {
            const panel = document.getElementById('helpPanel');
            if (panel.style.display === 'none' || !panel.style.display) {
                panel.style.display = 'block';
                setTimeout(() => panel.style.display = 'none', 5000); // 5秒后自动隐藏
            } else {
                panel.style.display = 'none';
            }
        }

        // 点击其他地方隐藏帮助面板
        document.addEventListener('click', function(e) {
            const helpPanel = document.getElementById('helpPanel');
            const helpButton = e.target.closest('button[onclick="toggleHelp()"]');

            if (!helpButton && !helpPanel.contains(e.target)) {
                helpPanel.style.display = 'none';
            }
        });

        function restoreAnswerState() {
            const answer = answers[currentQuestion];
            if (!answer) return;

            if (answer.type === 'choice') {
                const choices = document.querySelectorAll(`#q${currentQuestion} .choice-item`);
                choices.forEach((item, index) => {
                    const choice = ['A', 'B', 'C'][index];
                    if (choice === answer.answer) {
                        item.classList.add('selected');
                        if (!answer.correct) {
                            item.classList.add('incorrect');
                        }
                    }
                    if (choice === correctAnswers[currentQuestion]) {
                        item.classList.add('correct');
                    }
                    item.style.pointerEvents = 'none';
                });
            } else if (answer.type === 'fill') {
                const blanks = document.querySelectorAll(`#q${currentQuestion} .blank-input`);
                blanks.forEach((blank, index) => {
                    if (answer.answer[index]) {
                        blank.value = answer.answer[index];
                        blank.classList.add(answer.correct[index] ? 'correct' : 'incorrect');
                        blank.disabled = true;
                    }
                });
            }

            // 显示解析
            document.getElementById(`exp${currentQuestion}`).classList.add('show');
        }

        // 监听填空输入完成
        document.addEventListener('DOMContentLoaded', function() {
            const blanks = document.querySelectorAll('.blank-input');
            blanks.forEach((blank, index) => {
                blank.addEventListener('blur', function() {
                    const qIndex = parseInt(this.id.match(/\d+/)[0]);
                    checkFillAnswers(qIndex);
                });
            });
        });

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                previousQuestion();
            } else if (e.key === 'ArrowRight') {
                nextQuestion();
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const prevBtn = document.getElementById('prevBtn');
            if (prevBtn) prevBtn.disabled = true;
        });



        // 答题完成后跳转到结果页
        function completeQuiz() {
            // 切换到结果页面
            const questionContainer = document.getElementById('questionContainer');
            const resultPage = document.getElementById('resultPage');

            if (questionContainer && resultPage) {
                questionContainer.style.display = 'none';
                resultPage.style.display = 'block';
                currentPage = 'result';
            } else {
                console.error('无法找到结果页面元素');
                return;
            }

            const endTime = Date.now();
            const totalTimeSeconds = Math.floor((endTime - startTime) / 1000);

            // 计算得分
            let correctCount = 0;
            let totalScore = 0;
            let wrongQuestions = [];

            for (let i = 1; i <= totalQuestions; i++) {
                const answer = answers[i];
                if (answer) {
                    if (answer.type === 'choice' && answer.correct) {
                        correctCount++;
                        totalScore += 20; // 每题20分
                    } else if (answer.type === 'fill' && answer.correct.every(c => c)) {
                        correctCount++;
                        totalScore += 20;
                    } else {
                        wrongQuestions.push(i);
                    }
                } else {
                    // 未作答的题目也算错误
                    wrongQuestions.push(i);
                }
            }

            const incorrectCount = totalQuestions - correctCount;
            const accuracyRate = Math.round((correctCount / totalQuestions) * 100);

            // 更新结果页面数据
            document.getElementById('finalScore').textContent = totalScore;
            document.getElementById('correctCount').textContent = correctCount;
            document.getElementById('incorrectCount').textContent = incorrectCount;
            document.getElementById('accuracyRate').textContent = accuracyRate + '%';
            document.getElementById('totalTime').textContent = formatTime(totalTimeSeconds);

            // 生成学习建议
            generateLearningAdvice(accuracyRate, wrongQuestions, totalTimeSeconds);

            // 显示错题收藏提示
            if (wrongQuestions.length > 0) {
                document.getElementById('wrongQuestionTip').style.display = 'block';
                document.getElementById('wrongCount').textContent = wrongQuestions.length;
            }

            // 更新题目状态气泡
            updateQuestionBubbles();

            // 更新学习统计
            updateLearningStats(totalScore, accuracyRate, totalTimeSeconds);

            // 清除当前进度（已完成）
            clearProgress();
        }

        // 生成学习建议
        function generateLearningAdvice(accuracyRate, wrongQuestions, timeUsed) {
            let advice = '';

            if (accuracyRate >= 90) {
                advice = '🎉 优秀！您的听力水平很棒，建议挑战更高难度的题目，或者尝试不同类型的听力材料来保持和提升能力。';
            } else if (accuracyRate >= 70) {
                advice = '👍 良好！您已经掌握了基本的听力技巧，建议重点练习错题涉及的知识点，提高细节理解能力。';
            } else if (accuracyRate >= 50) {
                advice = '📚 需要加强！建议多听基础对话材料，重点训练关键词捕捉和语音识别能力。可以先从慢速材料开始练习。';
            } else {
                advice = '💪 继续努力！建议从基础听力训练开始，多听单词发音，培养语感。每天坚持练习15-20分钟会有明显进步。';
            }

            // 根据用时给出建议
            const avgTimePerQuestion = timeUsed / totalQuestions;
            if (avgTimePerQuestion > 120) { // 超过2分钟每题
                advice += ' 另外，建议提高答题速度，可以通过多做练习来提升反应能力。';
            }

            document.getElementById('adviceText').textContent = advice;
        }

        function updateQuestionBubbles() {
            for (let i = 1; i <= totalQuestions; i++) {
                const bubble = document.getElementById(`bubble-${i}`);
                const status = bubble?.querySelector('.question-status');
                const answer = answers[i];
                
                if (bubble && status) {
                    if (answer) {
                        let isCorrect = false;
                        
                        if (answer.type === 'choice') {
                            isCorrect = answer.correct === true;
                        } else if (answer.type === 'fill') {
                            isCorrect = Array.isArray(answer.correct) && answer.correct.every(c => c === true);
                        }
                        
                        if (isCorrect) {
                            bubble.className = 'question-bubble correct';
                            status.textContent = '✓';
                        } else {
                            bubble.className = 'question-bubble incorrect';
                            status.textContent = '✗';
                        }
                    } else {
                        bubble.className = 'question-bubble';
                        status.textContent = '?';
                    }
                }
            }
        }

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        // 结果页面功能
        function reviewQuestion(qIndex) {
            // 从结果页回到指定题目
            document.getElementById('resultPage').classList.add('hidden');
            document.getElementById('questionContainer').classList.remove('hidden');
            
            currentQuestion = qIndex;
            currentPage = 'question';
            
            // 显示指定题目
            for (let i = 1; i <= totalQuestions; i++) {
                document.getElementById(`q${i}`).classList.add('hidden');
            }
            document.getElementById(`q${qIndex}`).classList.remove('hidden');
            
            // 恢复答案状态
            restoreAnswerState();
            
            // 更新按钮状态
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            if (prevBtn) prevBtn.disabled = currentQuestion === 1;
            if (nextBtn) nextBtn.disabled = currentQuestion === totalQuestions;
        }

        function retryPaper() {
            selectPaper(selectedPaper);
        }

        function backToPaperSelection() {
            document.getElementById('resultPage').classList.add('hidden');
            document.getElementById('questionContainer').classList.add('hidden');
            document.getElementById('paperSelection').classList.remove('hidden');
            currentPage = 'paperSelection';
        }

        function shareResult() {
            // 分享功能 - 生成分享文本
            const score = document.getElementById('finalScore').textContent;
            const accuracy = document.getElementById('accuracyRate').textContent;
            const time = document.getElementById('totalTime').textContent;

            const shareText = `我在听力宝完成了听力练习！\n📊 得分：${score}分\n✅ 正确率：${accuracy}\n⏱️ 用时：${time}\n\n一起来挑战听力练习吧！`;

            if (navigator.share) {
                navigator.share({
                    title: '听力宝 - 我的答题成绩',
                    text: shareText
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    alert('成绩已复制到剪贴板！');
                });
            }
        }

        function goBack() {
            // 返回上一级
            if (currentPage === 'question') {
                backToPaperSelection();
            } else {
                alert('返回主页');
            }
        }

        // 从结果页返回做题界面
        function backToQuestion() {
            document.getElementById('resultPage').classList.add('hidden');
            document.getElementById('questionContainer').classList.remove('hidden');
            currentPage = 'question';
        }

        // 添加进度保存功能
        function saveProgress() {
            const progressData = {
                selectedPaper,
                currentQuestion,
                answers,
                startTime
            };
            localStorage.setItem('listeningProgress', JSON.stringify(progressData));
        }

        function loadProgress() {
            const saved = localStorage.getItem('listeningProgress');
            if (saved) {
                const data = JSON.parse(saved);
                selectedPaper = data.selectedPaper;
                currentQuestion = data.currentQuestion;
                answers = data.answers;
                startTime = data.startTime;
                return true;
            }
            return false;
        }

        function clearProgress() {
            localStorage.removeItem('listeningProgress');
        }

        // 学习统计功能
        function updateLearningStats(score, accuracy, timeUsed) {
            let stats = JSON.parse(localStorage.getItem('learningStats') || '{}');

            if (!stats.totalSessions) stats.totalSessions = 0;
            if (!stats.totalScore) stats.totalScore = 0;
            if (!stats.totalTime) stats.totalTime = 0;
            if (!stats.bestScore) stats.bestScore = 0;
            if (!stats.averageAccuracy) stats.averageAccuracy = 0;

            stats.totalSessions++;
            stats.totalScore += score;
            stats.totalTime += timeUsed;
            stats.bestScore = Math.max(stats.bestScore, score);
            stats.averageAccuracy = Math.round(((stats.averageAccuracy * (stats.totalSessions - 1)) + accuracy) / stats.totalSessions);
            stats.lastStudyDate = new Date().toISOString().split('T')[0];

            localStorage.setItem('learningStats', JSON.stringify(stats));
            return stats;
        }

        function getLearningStats() {
            return JSON.parse(localStorage.getItem('learningStats') || '{}');
        }

        // 显示学习统计（可以在结果页面调用）
        function showLearningStats() {
            const stats = getLearningStats();
            if (stats.totalSessions > 0) {
                const avgScore = Math.round(stats.totalScore / stats.totalSessions);
                const totalHours = Math.round(stats.totalTime / 3600 * 10) / 10;

                alert(`📊 您的学习统计：

🎯 总练习次数：${stats.totalSessions} 次
⭐ 最高得分：${stats.bestScore} 分
📈 平均得分：${avgScore} 分
✅ 平均正确率：${stats.averageAccuracy}%
⏰ 累计学习：${totalHours} 小时
📅 最近学习：${stats.lastStudyDate || '今天'}

继续加油！💪`);
            }
        }

        // 添加键盘快捷键支持
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                if (currentPage === 'question') {
                    switch(e.key) {
                        case 'ArrowLeft':
                            e.preventDefault();
                            previousQuestion();
                            break;
                        case 'ArrowRight':
                            e.preventDefault();
                            nextQuestion();
                            break;
                        case ' ':
                            e.preventDefault();
                            // 播放/暂停当前题目音频
                            togglePlay(currentQuestion);
                            break;
                        case '1':
                        case '2':
                        case '3':
                            e.preventDefault();
                            // 快速选择选项
                            const choices = document.querySelectorAll(`#q${currentQuestion} .choice-item`);
                            const choiceIndex = parseInt(e.key) - 1;
                            if (choices[choiceIndex]) {
                                const choice = ['A', 'B', 'C'][choiceIndex];
                                selectChoice(currentQuestion, choices[choiceIndex], choice);
                            }
                            break;
                    }
                }
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 原型初始化开始 ===');
            
            // 获取所有主要元素
            const paperSelection = document.getElementById('paperSelection');
            const questionContainer = document.getElementById('questionContainer');
            const resultPage = document.getElementById('resultPage');
            
            console.log('初始化元素:', {
                paperSelection: !!paperSelection,
                questionContainer: !!questionContainer,
                resultPage: !!resultPage
            });

            // 确保所有页面正确初始化
            if (paperSelection) {
                paperSelection.style.display = 'block';
                paperSelection.classList.remove('hidden');
            }
            if (questionContainer) {
                questionContainer.style.display = 'none';
                questionContainer.classList.add('hidden');
            }
            if (resultPage) {
                resultPage.style.display = 'none';
                resultPage.classList.add('hidden');
            }

            // 设置键盘快捷键
            setupKeyboardShortcuts();

            // 启动计时器
            startTimer();

            // 尝试加载之前的进度
            if (loadProgress()) {
                const continueStudy = confirm('检测到未完成的练习，是否继续？');
                if (continueStudy) {
                    selectPaper(selectedPaper);
                    switchQuestion(currentQuestion);
                } else {
                    clearProgress();
                }
            }
            
            // 原型加载完成提示
            setTimeout(() => {
                console.log('原型已完全加载，可以开始测试');
                console.log('测试步骤：');
                console.log('1. 点击任意试卷进入答题');
                console.log('2. 完成5道题目');
                console.log('3. 自动跳转到报告页');
            }, 500);
            
            console.log('=== 原型初始化完成 ===');
        });
    </script>
</body>
</html>







