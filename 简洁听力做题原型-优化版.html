<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>听力练习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.5;
            overflow: hidden;
        }

        /* 主容器 */
        .container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* 题目卡片 */
        .question-card {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 0 50px;
            position: relative;
        }

        /* 题量信息 */
        .question-counter {
            text-align: center;
            padding: 16px 20px 0;
            font-size: 14px;
            color: #666;
        }

        /* 录音播放器 */
        .audio-section {
            padding: 16px 20px 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .audio-player {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .play-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #007AFF;
            border: none;
            color: white;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .play-btn:hover {
            transform: scale(1.05);
            background: #0056CC;
        }

        .play-btn:active {
            transform: scale(0.95);
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            margin-bottom: 3px;
        }

        .progress-bar {
            height: 2px;
            background: #e0e0e0;
            border-radius: 1px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: #007AFF;
            width: 0%;
            transition: width 0.1s;
        }

        /* 题目内容 */
        .question-content {
            padding: 20px;
        }

        .question-text {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 16px;
            color: #1d1d1f;
        }

        .question-image {
            width: 100%;
            height: auto;
            border-radius: 8px;
            margin-bottom: 16px;
            background: #f0f0f0;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        /* 选择题 */
        .choices {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .choice-item {
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 14px 16px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 15px;
            line-height: 1.4;
        }

        .choice-item:hover {
            background: #f8f9fa;
            border-color: #007AFF;
        }

        .choice-item.selected {
            background: #007AFF10;
            border-color: #007AFF;
        }

        .choice-item.correct {
            background: #34C75910;
            border-color: #34C759;
        }

        .choice-item.incorrect {
            background: #FF3B3010;
            border-color: #FF3B30;
        }

        /* 填空题 */
        .fill-blank {
            margin-bottom: 16px;
        }

        .fill-blank-text {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 16px;
        }

        .blank-input {
            border: none;
            border-bottom: 1px solid #e0e0e0;
            padding: 2px 6px;
            font-size: 16px;
            outline: none;
            min-width: 80px;
            text-align: center;
            transition: border-color 0.2s;
            background: transparent;
            margin: 0 2px;
        }

        .blank-input:focus {
            border-bottom-color: #007AFF;
        }

        .blank-input.correct {
            border-bottom-color: #34C759;
            background: #34C75910;
        }

        .blank-input.incorrect {
            border-bottom-color: #FF3B30;
            background: #FF3B3010;
        }

        /* 优化版导航按钮 */
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
            color: #007AFF;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .nav-button:hover {
            background: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-50%) scale(1.05);
        }

        .nav-button:active {
            transform: translateY(-50%) scale(0.95);
        }

        .nav-prev {
            left: 12px;
        }

        .nav-next {
            right: 12px;
        }

        .nav-button:disabled {
            opacity: 0.4;
            cursor: not-allowed;
            background: rgba(255, 255, 255, 0.6);
            color: #ccc;
            transform: translateY(-50%);
        }

        /* 半透明遮罩提示 */
        .nav-hint {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: rgba(0, 122, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #007AFF;
            opacity: 0.6;
            transition: opacity 0.2s;
            pointer-events: none;
        }

        .nav-hint.prev {
            left: 16px;
        }

        .nav-hint.next {
            right: 16px;
        }

        /* 答案解析 */
        .answer-explanation {
            margin-top: 16px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 2px solid #007AFF;
            display: none;
        }

        .answer-explanation.show {
            display: block;
            animation: fadeIn 0.2s ease-out;
        }

        .explanation-text {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .question-card {
                margin: 0 30px;
                border-radius: 12px;
            }
            
            .nav-button {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
            
            .nav-prev {
                left: 8px;
            }
            
            .nav-next {
                right: 8px;
            }
        }

        @media (max-width: 375px) {
            .question-card {
                margin: 0 20px;
            }
            
            .nav-button {
                width: 30px;
                height: 30px;
                font-size: 13px;
            }
            
            .nav-prev {
                left: 6px;
            }
            
            .nav-next {
                right: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 优化版导航按钮 -->
        <button class="nav-button nav-prev" onclick="previousQuestion()" id="prevBtn">‹</button>
        
        <!-- 题目卡片 -->
        <div class="question-card">
            
            <!-- 第1题：文本选择题 -->
            <div class="question-content" id="q1">
                <div class="question-counter">1/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(1)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time1">0:00</span>
                                <span>2:30</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(1, event)">
                                <div class="progress-fill" id="progress1"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">What is the man's plan for the weekend?</div>
                    <div class="choices">
                        <div class="choice-item" onclick="selectChoice(1, this, 'A')">Go hiking in the mountains</div>
                        <div class="choice-item" onclick="selectChoice(1, this, 'B')">Visit his parents in the countryside</div>
                        <div class="choice-item" onclick="selectChoice(1, this, 'C')">Stay at home and relax</div>
                    </div>
                    <div class="answer-explanation" id="exp1">
                        <div class="explanation-text">根据对话内容，男士提到"I'm thinking of going hiking with some friends this weekend"，因此正确答案是A。</div>
                    </div>
                </div>
            </div>

            <!-- 第2题：图片选择题 -->
            <div class="question-content hidden" id="q2">
                <div class="question-counter">2/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(2)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time2">0:00</span>
                                <span>1:45</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(2, event)">
                                <div class="progress-fill" id="progress2"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">根据听到的内容，选择正确的地图路线：</div>
                    <div class="question-image">地图图片占位符</div>
                    <div class="choices">
                        <div class="choice-item" onclick="selectChoice(2, this, 'A')">Route through the park</div>
                        <div class="choice-item" onclick="selectChoice(2, this, 'B')">Route along the river</div>
                        <div class="choice-item" onclick="selectChoice(2, this, 'C')">Route via the main road</div>
                    </div>
                    <div class="answer-explanation" id="exp2">
                        <div class="explanation-text">录音中提到了"take the scenic route through the central park"，因此应该选择经过公园的路线A。</div>
                    </div>
                </div>
            </div>

            <!-- 第3题：填空题 -->
            <div class="question-content hidden" id="q3">
                <div class="question-counter">3/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(3)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time3">0:00</span>
                                <span>1:20</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(3, event)">
                                <div class="progress-fill" id="progress3"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">完成句子：</div>
                    <div class="fill-blank">
                        <div class="fill-blank-text">
                            The meeting will be held at 
                            <input type="text" class="blank-input" id="blank3_1" placeholder="时间" oninput="saveFillAnswer(3, 0, this.value)">
                            on 
                            <input type="text" class="blank-input" id="blank3_2" placeholder="日期" oninput="saveFillAnswer(3, 1, this.value)">
                            in Room 301.
                        </div>
                    </div>
                    <div class="answer-explanation" id="exp3">
                        <div class="explanation-text">根据录音内容，会议时间是"3 o'clock"，日期是"next Monday"。</div>
                    </div>
                </div>
            </div>

            <!-- 第4题：图片填空题 -->
            <div class="question-content hidden" id="q4">
                <div class="question-counter">4/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(4)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time4">0:00</span>
                                <span>2:10</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(4, event)">
                                <div class="progress-fill" id="progress4"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">根据图表信息，完成句子：</div>
                    <div class="question-image">数据图表占位符</div>
                    <div class="fill-blank">
                        <div class="fill-blank-text">
                            The highest sales occurred in 
                            <input type="text" class="blank-input" id="blank4_1" placeholder="月份" oninput="saveFillAnswer(4, 0, this.value)">
                            with a total of 
                            <input type="text" class="blank-input" id="blank4_2" placeholder="数字" oninput="saveFillAnswer(4, 1, this.value)">
                            units sold.
                        </div>
                    </div>
                    <div class="answer-explanation" id="exp4">
                        <div class="explanation-text">从图表可以看出，12月份的销售额最高，达到了1500个单位。</div>
                    </div>
                </div>
            </div>

            <!-- 第5题：综合选择题 -->
            <div class="question-content hidden" id="q5">
                <div class="question-counter">5/5</div>
                <div class="audio-section">
                    <div class="audio-player">
                        <button class="play-btn" onclick="togglePlay(5)">▶</button>
                        <div style="flex: 1;">
                            <div class="time-display">
                                <span id="time5">0:00</span>
                                <span>1:55</span>
                            </div>
                            <div class="progress-bar" onclick="seekTo(5, event)">
                                <div class="progress-fill" id="progress5"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="question-content">
                    <div class="question-text">根据听到的对话和下面的时间表，选择正确的选项：</div>
                    <div class="question-image">时间表占位符</div>
                    <div class="choices">
                        <div class="choice-item" onclick="selectChoice(5, this, 'A')">They will meet at 9:00 AM</div>
                        <div class="choice-item" onclick="selectChoice(5, this, 'B')">They will meet at 2:00 PM</div>
                        <div class="choice-item" onclick="selectChoice(5, this, 'C')">They will meet at 11:00 AM</div>
                    </div>
                    <div class="answer-explanation" id="exp5">
                        <div class="explanation-text">对话中提到"Let's meet during the project review at 2 PM"，因此正确答案是B。</div>
                    </div>
                </div>
            </div>
        </div>

        <button class="nav-button nav-next" onclick="nextQuestion()" id="nextBtn">›</button>
    </div>

    <script>
        // 全局状态
        let currentQuestion = 1;
        const totalQuestions = 5;
        let answers = {};
        let audioStates = {};

        // 题目答案
        const correctAnswers = {
            1: 'A',
            2: 'A',
            3: ['3 o\'clock', 'next monday'],
            4: ['December', '1500'],
            5: 'B'
        };

        // 音频播放功能
        function togglePlay(qIndex) {
            if (!audioStates[qIndex]) {
                audioStates[qIndex] = {
                    isPlaying: false,
                    currentTime: 0,
                    totalTime: [150, 105, 80, 130, 115][qIndex-1],
                    interval: null
                };
            }

            const state = audioStates[qIndex];
            state.isPlaying = !state.isPlaying;

            const playBtn = document.querySelector(`#q${qIndex} .play-btn`);
            const timeEl = document.getElementById(`time${qIndex}`);
            const progressEl = document.getElementById(`progress${qIndex}`);

            if (state.isPlaying) {
                playBtn.innerHTML = '❚❚';
                state.interval = setInterval(() => {
                    if (state.currentTime < state.totalTime) {
                        state.currentTime++;
                        updateAudioDisplay(qIndex);
                    } else {
                        stopAudio(qIndex);
                    }
                }, 100);
            } else {
                stopAudio(qIndex);
            }
        }

        function stopAudio(qIndex) {
            const state = audioStates[qIndex];
            if (state) {
                state.isPlaying = false;
                clearInterval(state.interval);
                const playBtn = document.querySelector(`#q${qIndex} .play-btn`);
                playBtn.innerHTML = '▶';
            }
        }

        function updateAudioDisplay(qIndex) {
            const state = audioStates[qIndex];
            const timeEl = document.getElementById(`time${qIndex}`);
            const progressEl = document.getElementById(`progress${qIndex}`);
            
            const mins = Math.floor(state.currentTime / 60);
            const secs = state.currentTime % 60;
            timeEl.textContent = `${mins}:${secs.toString().padStart(2, '0')}`;
            
            const progress = (state.currentTime / state.totalTime) * 100;
            progressEl.style.width = progress + '%';
        }

        function seekTo(qIndex, event) {
            if (!audioStates[qIndex]) return;
            
            const rect = event.target.getBoundingClientRect();
            const clickX = event.clientX - rect.left;
            const percentage = clickX / rect.width;
            
            audioStates[qIndex].currentTime = Math.floor(percentage * audioStates[qIndex].totalTime);
            updateAudioDisplay(qIndex);
        }

        // 选择题功能
        function selectChoice(qIndex, element, choice) {
            // 清除其他选择
            const choices = element.parentNode.querySelectorAll('.choice-item');
            choices.forEach(item => {
                item.classList.remove('selected', 'correct', 'incorrect');
                item.style.pointerEvents = 'none';
            });

            // 添加选择状态
            element.classList.add('selected');

            // 保存答案
            answers[qIndex] = {
                type: 'choice',
                answer: choice,
                correct: choice === correctAnswers[qIndex]
            };

            // 显示正确答案
            setTimeout(() => {
                choices.forEach(item => item.style.pointerEvents = 'auto');
                
                const correctIndex = ['A', 'B', 'C'].indexOf(correctAnswers[qIndex]);
                choices[correctIndex].classList.add('correct');
                
                if (choice !== correctAnswers[qIndex]) {
                    element.classList.add('incorrect');
                }

                document.getElementById(`exp${qIndex}`).classList.add('show');
            }, 600);
        }

        // 填空题功能
        function saveFillAnswer(qIndex, blankIndex, value) {
            if (!answers[qIndex]) {
                answers[qIndex] = {
                    type: 'fill',
                    answer: ['', ''],
                    correct: [false, false]
                };
            }
            
            answers[qIndex].answer[blankIndex] = value;
            answers[qIndex].correct[blankIndex] = 
                value.toLowerCase() === correctAnswers[qIndex][blankIndex].toLowerCase();
        }

        function checkFillAnswers(qIndex) {
            const blanks = document.querySelectorAll(`#q${qIndex} .blank-input`);
            const allFilled = Array.from(blanks).every(b => b.value.trim());
            
            if (!allFilled) return;

            blanks.forEach((blank, index) => {
                blank.disabled = true;
                const isCorrect = blank.value.toLowerCase() === correctAnswers[qIndex][index].toLowerCase();
                
                if (isCorrect) {
                    blank.classList.add('correct');
                } else {
                    blank.classList.add('incorrect');
                    blank.value = correctAnswers[qIndex][index];
                }
            });

            document.getElementById(`exp${qIndex}`).classList.add('show');
        }

        // 题目切换
        function previousQuestion() {
            if (currentQuestion > 1) {
                switchQuestion(currentQuestion - 1);
            }
        }

        function nextQuestion() {
            if (currentQuestion < totalQuestions) {
                switchQuestion(currentQuestion + 1);
            }
        }

        function switchQuestion(newIndex) {
            // 停止当前音频
            stopAudio(currentQuestion);

            // 隐藏当前题目
            document.getElementById(`q${currentQuestion}`).classList.add('hidden');
            
            // 显示新题目
            document.getElementById(`q${newIndex}`).classList.remove('hidden');
            
            currentQuestion = newIndex;

            // 更新按钮状态
            document.getElementById('prevBtn').disabled = currentQuestion === 1;
            document.getElementById('nextBtn').disabled = currentQuestion === totalQuestions;

            // 恢复之前的选择状态
            restoreAnswerState();
        }

        function restoreAnswerState() {
            const answer = answers[currentQuestion];
            if (!answer) return;

            if (answer.type === 'choice') {
                const choices = document.querySelectorAll(`#q${currentQuestion} .choice-item`);
                choices.forEach((item, index) => {
                    const choice = ['A', 'B', 'C'][index];
                    if (choice === answer.answer) {
                        item.classList.add('selected');
                        if (!answer.correct) {
                            item.classList.add('incorrect');
                        }
                    }
                    if (choice === correctAnswers[currentQuestion]) {
                        item.classList.add('correct');
                    }
                    item.style.pointerEvents = 'none';
                });
            } else if (answer.type === 'fill') {
                const blanks = document.querySelectorAll(`#q${currentQuestion} .blank-input`);
                blanks.forEach((blank, index) => {
                    if (answer.answer[index]) {
                        blank.value = answer.answer[index];
                        blank.classList.add(answer.correct[index] ? 'correct' : 'incorrect');
                        blank.disabled = true;
                    }
                });
            }

            document.getElementById(`exp${currentQuestion}`).classList.add('show');
        }

        // 监听填空输入
        document.addEventListener('DOMContentLoaded', function() {
            const blanks = document.querySelectorAll('.blank-input');
            blanks.forEach((blank, index) => {
                blank.addEventListener('blur', function() {
                    const qIndex = parseInt(this.id.match(/\d+/)[0]);
                    checkFillAnswers(qIndex);
                });
            });

            // 初始化按钮状态
            document.getElementById('prevBtn').disabled = true;

            // 键盘导航
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowLeft') {
                    previousQuestion();
                } else if (e.key === 'ArrowRight') {
                    nextQuestion();
                }
            });
        });
    </script>
</body>
</html>